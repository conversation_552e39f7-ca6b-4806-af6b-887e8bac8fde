#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的文章内容和阅读量提取
基于spider_readnum.py的成功实现
"""

import sys
import traceback
from batch_readnum_spider import BatchReadnumSpider

def test_optimized_extraction():
    """测试优化后的提取功能"""
    try:
        print("🚀 测试优化后的文章内容和阅读量提取")
        print("📋 基于spider_readnum.py的成功实现")
        print("="*60)
        
        # 创建爬虫实例
        spider = BatchReadnumSpider()
        
        # 加载认证信息
        if not spider.load_auth_info():
            print("❌ 认证信息加载失败")
            return False
        
        print("✅ 认证信息加载成功")
        
        # 获取文章列表
        print("\n📡 获取文章列表...")
        articles = spider.get_article_list(begin_page=0, count=2)  # 获取2篇文章测试
        
        if not articles:
            print("❌ 未获取到文章列表")
            return False
        
        print(f"✅ 获取到 {len(articles)} 篇文章")
        
        # 测试每篇文章
        success_count = 0
        for i, article in enumerate(articles):
            print(f"\n{'='*50}")
            print(f"📖 测试第 {i+1} 篇文章: {article['title']}")
            print(f"🔗 文章URL: {article['url'][:80]}...")
            
            # 提取文章内容和统计数据
            print(f"\n📊 开始提取文章内容和统计数据...")
            result = spider.extract_article_content_and_stats(article['url'])
            
            if result:
                if result.get('error'):
                    print(f"⚠️ 提取遇到问题: {result['error']}")
                    continue
                else:
                    print("✅ 成功提取文章数据:")
                    print(f"   📝 标题: {result['title']}")
                    print(f"   📊 阅读量: {result['read_count']}")
                    print(f"   👍 点赞数: {result['like_count']}")
                    print(f"   📤 分享数: {result['share_count']}")
                    print(f"   📄 内容长度: {len(result['content'])} 字符")
                    print(f"   🕒 抓取时间: {result['crawl_time']}")
                    
                    # 显示内容预览
                    if result['content']:
                        preview = result['content'][:150] + "..." if len(result['content']) > 150 else result['content']
                        print(f"   📖 内容预览: {preview}")
                    
                    success_count += 1
                    
                    # 检查是否有统计数据
                    has_stats = any([
                        result['read_count'] > 0,
                        result['like_count'] > 0,
                        result['share_count'] > 0
                    ])
                    
                    if has_stats:
                        print(f"🎉 找到有统计数据的文章！")
                        print(f"   阅读量: {result['read_count']}")
                        print(f"   点赞数: {result['like_count']}")
                        print(f"   分享数: {result['share_count']}")
            else:
                print("❌ 提取文章数据失败")
        
        print(f"\n📊 测试总结:")
        print(f"   成功提取: {success_count}/{len(articles)} 篇文章")
        print(f"   内容提取: {'✅ 正常' if success_count > 0 else '❌ 失败'}")
        print(f"   优化效果: {'✅ 成功应用spider_readnum.py的实现' if success_count > 0 else '❌ 需要进一步调试'}")
        
        return success_count > 0
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_optimized_extraction()
    
    if success:
        print("\n🎉 优化后的提取功能测试成功！")
        print("\n💡 总结:")
        print("   ✅ 已应用spider_readnum.py中验证成功的正则表达式")
        print("   ✅ 文章内容提取功能正常")
        print("   ✅ 统计数据提取逻辑正常")
        print("   ⚠️ 部分文章可能没有公开统计数据（这是正常现象）")
        print("\n💡 接下来可以:")
        print("   1. 运行批量抓取: python batch_readnum_spider.py")
        print("   2. 尝试其他公众号的文章")
        print("   3. 检查是否有公开统计数据的文章")
    else:
        print("\n❌ 优化后的提取功能测试失败")
        print("💡 建议:")
        print("   1. 检查认证信息是否有效")
        print("   2. 检查网络连接")
        print("   3. 查看debug文件夹中的HTML文件")

if __name__ == "__main__":
    main()
