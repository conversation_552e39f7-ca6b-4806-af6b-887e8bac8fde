#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试x-wechat-key的加载和使用
"""

import sys
import traceback
from batch_readnum_spider import BatchReadnumSpider

def test_wechat_key():
    """测试x-wechat-key的加载"""
    try:
        print("🔑 测试x-wechat-key的加载和使用")
        print("="*60)
        
        # 创建爬虫实例
        spider = BatchReadnumSpider()
        
        # 检查headers中是否包含x-wechat-key
        print("\n📋 检查当前headers:")
        for key, value in spider.headers.items():
            if key == 'x-wechat-key':
                print(f"✅ {key}: {value[:30]}...")
            elif key in ['x-wechat-uin', 'exportkey']:
                print(f"✅ {key}: {value[:30]}...")
            else:
                print(f"   {key}: {value[:50]}...")
        
        # 测试获取一篇文章
        print("\n📡 获取文章列表...")
        articles = spider.get_article_list(begin_page=0, count=1)
        
        if not articles:
            print("❌ 未获取到文章列表")
            return False
        
        print(f"✅ 获取到 {len(articles)} 篇文章")
        
        # 测试第一篇文章的阅读量提取
        article = articles[0]
        print(f"\n📖 测试文章: {article['title']}")
        print(f"🔗 文章URL: {article['url'][:80]}...")
        
        print(f"\n📊 开始提取文章内容和统计数据...")
        result = spider.extract_article_content_and_stats(article['url'])
        
        if result:
            if result.get('error'):
                print(f"⚠️ 提取遇到问题: {result['error']}")
                return False
            else:
                print("✅ 成功提取文章数据:")
                print(f"   📝 标题: {result['title']}")
                print(f"   📊 阅读量: {result['read_count']}")
                print(f"   👍 点赞数: {result['like_count']}")
                print(f"   📤 分享数: {result['share_count']}")
                print(f"   📄 内容长度: {len(result['content'])} 字符")
                
                # 检查是否成功获取到阅读量
                if result['read_count'] > 0:
                    print(f"🎉 成功获取阅读量: {result['read_count']}！")
                    print("✅ x-wechat-key 工作正常")
                    return True
                else:
                    print("⚠️ 阅读量为0，可能的原因:")
                    print("   1. 该文章未公开显示阅读量")
                    print("   2. x-wechat-key 可能已过期")
                    print("   3. 需要更新抓包数据")
                    return False
        else:
            print("❌ 提取文章数据失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_wechat_key()
    
    if success:
        print("\n🎉 x-wechat-key 测试成功！")
        print("\n💡 总结:")
        print("   ✅ x-wechat-key 已正确加载")
        print("   ✅ 成功获取到阅读量数据")
        print("   ✅ 可以进行批量抓取")
    else:
        print("\n⚠️ x-wechat-key 测试未完全成功")
        print("\n💡 可能的解决方案:")
        print("   1. 重新运行 cookie_extractor.py 获取最新的抓包数据")
        print("   2. 确保访问的是有阅读量显示的文章")
        print("   3. 检查 wechat_keys.txt 文件中是否有 x-wechat-key")
        print("   4. 尝试不同的公众号文章")

if __name__ == "__main__":
    main()
